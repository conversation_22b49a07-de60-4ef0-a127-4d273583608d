[package]
name = 'DexlynClmm'
version = '0.0.2'

[addresses]
std = "0x1"
aptos_std = "0x1"
aptos_framework = "0x1"
dexlyn_clmm = "0x9f1feff9a32d2017ae47ed122d2c6b0ff8cd3143f12ec9211344261e0bcb6cfb"
dexlyn_integrate = "0x9f1feff9a32d2017ae47ed122d2c6b0ff8cd3143f12ec9211344261e0bcb6cfb"
integer_mate = "0x9f1feff9a32d2017ae47ed122d2c6b0ff8cd3143f12ec9211344261e0bcb6cfb"


[dependencies.AptosTokenObjects]
git = 'https://github.com/Entropy-Foundation/aptos-core.git'
subdir = 'aptos-move/framework/aptos-token-objects'
rev = 'a8042dd17a3699e91c293a41b15b5d547c833a5b'

[dependencies.SupraFramework]
git = 'https://github.com/Entropy-Foundation/aptos-core.git'
subdir = 'aptos-move/framework/supra-framework'
rev = 'a8042dd17a3699e91c293a41b15b5d547c833a5b'


[dependencies.IntegerMate]
local = "../IntegerMate"